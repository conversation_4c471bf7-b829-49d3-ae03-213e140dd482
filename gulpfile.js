"use strict";

var gulp        = require('gulp');
var tsc         = require('gulp-typescript');
var sourcemaps  = require('gulp-sourcemaps');
var mocha       = require('gulp-mocha');

var tslint 	= require('gulp-tslint');
var runSequence = require('run-sequence');
var merge = require('merge2');


var istanbul = require('gulp-istanbul');
var remapIstanbul = require('remap-istanbul/lib/gulpRemapIstanbul');

gulp.task('tslint', () => {
    return gulp.src(['src/**/*.ts', '!src/test/**/*.ts'])
        .pipe(tslint({
            formatter: "verbose"
        }))
        .pipe(tslint.report());
});

gulp.task('compile', () => {
    let errors = false;
    const tsProject = tsc.createProject('./tsconfig.json');
    const tsResult = gulp.src(['src/**/*.ts'])
        .pipe(sourcemaps.init())
        .pipe(tsProject())
        .on('error', function() { errors = true; })
        .on('end', function() { if (errors) process.exit(1);});

    return merge([
        tsResult.dts.pipe(gulp.dest('lib')),
        tsResult.js.pipe(sourcemaps.write())
            .pipe(gulp.dest('lib'))
    ]);
});


gulp.task('compile', () => {
    let errors = false;
const tsProject = tsc.createProject('tsconfig.json');
return gulp.src(['src/**/*.ts'])
    .pipe(sourcemaps.init())
    .pipe(tsProject())
    .on('error', function() { errors = true; })
    .on('end', function() { if (errors) process.exit(1);})
    .pipe(sourcemaps.write())
    .pipe(gulp.dest('lib'));
});

gulp.task('pre-test', function () {
    return gulp.src(['lib/skywind/**/*.js', 'lib/index.js'])
    // Covering files
        .pipe(istanbul({includeUntested: true}))
        // Force `require` to return covered files
        .pipe(istanbul.hookRequire());
});

gulp.task('test-server', ['pre-test'], () => {
    return gulp.src(['lib/test/**/*.spec.js'])
        .pipe(mocha({
            "reporter": "mocha-jenkins-reporter",
            "reporterOptions": {
                "junit_report_name": "Tests",
                "junit_report_path": "coverage/xunit.xml",
                "junit_report_stack": 1
            }}))
        .pipe(istanbul.writeReports());
});

gulp.task('remap-istanbul', function () {
    return gulp.src('./coverage/coverage-final.json', { base: '.' })
        .pipe(remapIstanbul({
            reports: {
                'lcovonly': './coverage/remap/lcov.info',
                'json': './coverage/remap/coverage.json',
                'html': './coverage/remap/html-report',
                'cobertura': './coverage/remap/cobertura.xml'
            }
        }));
});

gulp.task('unit-test', ['compile'], () => {
    runSequence('test-server', 'remap-istanbul', function (err) {
        if (err) return process.exit(1);
        process.exit(0);
    });
});

gulp.task('watch-server', () => {
    gulp.watch(['src/**/*.ts', 'resources/*.*'], function () {
        runSequence('compile'/* , 'test-server-light' */);
    });
});

gulp.task('default', ['compile'], () => {
});

gulp.task('watch', () => {
    runSequence('compile', 'unit-test',  'watch-server');
});
